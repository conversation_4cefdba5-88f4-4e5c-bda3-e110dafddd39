using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Rule to check if the passed end date should be greater than start date or not
    /// </summary>
    public class EndDateShouldBeGreaterThanStartDateRule : IBusinessRule
    {
        private readonly DateTime _startDate;
        private readonly DateTime _endDate;
        private readonly ITranslationService _translationService ;

        public EndDateShouldBeGreaterThanStartDateRule(ITranslationService translationService, DateTime startDate, DateTime endDate)
        {
            _startDate = startDate;
            _endDate = endDate;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidDateRange);

        public bool IsBroken()
        {
            // Compare only the date parts (ignore time components) to avoid timezone issues
            var startDateOnly = _startDate.Date;
            var endDateOnly = _endDate.Date;

            return startDateOnly > endDateOnly;
        }
    }
}
