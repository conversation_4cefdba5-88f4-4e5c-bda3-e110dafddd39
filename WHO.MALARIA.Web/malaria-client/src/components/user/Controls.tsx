﻿import React, { useState } from "react";
import { FormGroup, FormHelperText } from "@mui/material";
import FaceIcon from "@mui/icons-material/Face";

import { TabModel } from "../../models/TabModel";
import { StepperModel } from "../../models/StepperModel";
import MultiSelectModel from "../../models/MultiSelectModel";
import Dropdown from "../controls/Dropdown";
import MultiSelect from "../controls/MultiSelect";
import TextBox from "../controls/TextBox";
import WHOTabs from "../controls/WHOTabs";
import PillBox from "../controls/PillBox";
import ToggleSwitch from "../controls/ToggleSwitch";
import FileUploader from "../controls/FileUploader";
import FilePreview from "../controls/FilePreview";
import RadioButtonGroup from "../controls/RadioButtonGroup";
import Checkbox from "../controls/Checkbox";
import DateTimePicker from "../controls/DateTimePicker";
import DatePicker from "../controls/DatePicker";
import WHOStepper from "../controls/WHOStepper";
import CheckboxList from "../controls/CheckboxList";
import PieChart from "../controls/PieChart";
import LineChart from "../controls/LineChart";
import BarChart from "../controls/BarChart";
import { getCall, postCall } from "../../services/api";

// // import { Date | null } from "@material-ui/pickers/constants/prop-types";

const Controls = () => {
  const [files, setFiles] = useState<any>();
  const [date, setDate] = useState<Date>(new Date());
  const [activeStep, setActiveStep] = useState<number>(0);
  const [checked, setChecked] = useState<Array<string | number>>([11, 22]);

  const [barType, setBarType] = useState("bar");

  // line chart x-axis tiles
  let lineChartxAxisTiles = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"];
  // Line chart data array
  let lineChartData = [
    [121, 296, 310, 212, 240, 156, 98],
    [203, 276, 310, 212, 200, 171, 98],
    [156, 296, 310, 204, 191, 156, 45],
  ];
  // Line chart xAxisTitle
  let xAxisTitle = "Months";
  // Line chart title
  let lineChartTitle = "";
  // Line chart type
  let lineChartType = "line";

  // BAR chart y-axis tiles
  let barChartCategories = ["Q1", "Q2", "Q3", "Q4"];
  // BAR chart data array
  let barData = [
    [100, 123, 234, 343],
    [180, 200, 150, 260],
    [18, 52, 225, 300],
    [95, 103, 200, 310],
  ];
  // BAR ChartCategoryAxisTitle
  let chartCategoryAxisTitle = "Months";
  // BAR chart title
  let chartTitle = "";

  // PIE chart data array of objects
  let pieData = [
    { category: "0-14", value: 0.2545 },
    { category: "15-24", value: 0.1552 },
    { category: "25-54", value: 0.4059 },
    { category: "55-64", value: 0.0911 },
    { category: "65+", value: 0.0933 },
  ];
  // PIE chart title
  let pieTitle = "";
  // Label Text
  let pieLabel = "Label text";

  const handleChange = (e: any) => {
    // console.log(e.target.value)
    setBarType(e.target.value);
  };

  return (
    <div className="container-fluid">
      <div className="row">
        <h3>WHO Malaria Toolkit - Controls </h3>
        <hr />

        {/* Pie CHART */}
        <div className="container-fluid">
          <div className="row">
            <div className="col-lg-6 col-md-12 col-sm-12 col-xs-12 ml-5 mt-3">
              <h4 className="text-center">Pie Chart</h4>
              <PieChart
                title={pieTitle}
                data={pieData}
                showLabel
                labelText={pieLabel}
              />
            </div>

            {/* Line CHART */}
            <div className="col-lg-6 col-md-12 col-sm-12 col-xs-12 ml-5 mt-3">
              <h4 className="text-center">Line Chart</h4>
              <LineChart
                chartTitle={lineChartTitle}
                xAxisArr={lineChartxAxisTiles}
                lineData={lineChartData}
                xAxisTitle={xAxisTitle}
                lineChartType={lineChartType}
              />
            </div>

            {/* BAR CHART */}
            <div className="col-lg-6 col-md-12 col-sm-12 col-xs-12 ml-5 mt-3">
              <h4 className="text-center">Bar Chart</h4>
              <div>
                <select onChange={handleChange}>
                  <option value="bar" selected>
                    Horizontal
                  </option>
                  <option value="column">Vertical</option>
                </select>
              </div>
              <BarChart
                chartTitle={chartTitle}
                displayType={barType}
                chartCategoryAxisTitle={chartCategoryAxisTitle}
                barChartCategories={barChartCategories}
                barData={barData}
              />
            </div>
          </div>
        </div>

        <div className="col-md-12 ml-5 mt-3">
          <div className="col-md-12 ml-5 mt-3">
            <h4>Textbox</h4>
            <TextBox error helperText="mandatory field" />
          </div>
          <div className="col-md-12 ml-5 mt-3">
            <h4>Dropdown</h4>
            <Dropdown
              id="country"
              name="country"
              fullWidth
              label="Country"
              options={countries}
              value="1e771f2e-8476-465f-8eff-bdbbc2b31e9e"
            />
          </div>
          <div className="col-md-12 ml-5 mt-3">
            <h4>Multi-Dropdown</h4>
            <MultiSelect
              id="country"
              label="Country"
              options={countries}
              values={[countries[0], countries[1]]}
              onUpdate={(values: Array<MultiSelectModel>) => {}}
            />
          </div>
          <div className="col-md-12 ml-5 mt-3">
            <h4>Tabs</h4>
            <div className="app-tab-wrapper">
              <WHOTabs tabs={tabs} scrollable={true} />
            </div>
          </div>
          <div className="col-md-12 ml-5 mt-3">
            <h4>Pillbox</h4>
            <PillBox
              icon={<FaceIcon />}
              size="small"
              label="Deletable Primary"
              color="primary"
              onDelete={() => {}}
            />
          </div>

          <div className="col-md-12 ml-5 mt-3">
            <h4>Toggle Switch</h4>
            <ToggleSwitch
              checked={true}
              onChange={() => {}}
              color="primary"
              name="switch"
            />
          </div>
          <div className="col-md-12 ml-5 mt-3">
            <h4>File Uploader</h4>
            <FileUploader
              id="demo"
              multiple
              onChange={(evt: React.ChangeEvent<HTMLInputElement>) =>
                setFiles(evt.target.files)
              }
            >
              <FilePreview
                files={files}
                showPreviewText
                onDelete={(deletedFile: File) => {
                  const remainingFile = Array.from(files as FileList).filter(
                    (file: File) => file !== deletedFile
                  );
                  setFiles(remainingFile);
                }}
              />
            </FileUploader>
          </div>

          <div className="col-md-12 ml-5 mt-3">
            <h4>Radio Button Group</h4>
            <RadioButtonGroup
              id="gender"
              name="gender"
              row
              color="primary"
              helperText="Error Message"
              options={[
                new MultiSelectModel(1, "Male"),
                new MultiSelectModel(2, "Female"),
                new MultiSelectModel(3, "Others"),
              ]}
              value={1}
            />
          </div>

          <div className="col-md-12 ml-5 mt-3">
            <h4>Checkbox</h4>
            <FormGroup row>
              <Checkbox label="Male" color="primary" checked />
              <Checkbox label="Female" color="primary" />
              <Checkbox label="Others" color="primary" />
            </FormGroup>
            <FormHelperText>Error Message</FormHelperText>
          </div>

          <div className="col-md-12 ml-5 mt-3">
            <h4>Date Time</h4>
            <DateTimePicker
              label="Select Date & Time"
              value={date}
              onChange={setDate}
              error
              helperText="Ex. Invalid Date"
            />
          </div>

          <div className="col-md-12 ml-5 mt-3">
            <h4>Date</h4>
            <DatePicker
              label="Select Date"
              value={date}
              onChange={setDate}
              error
              helperText="Ex. Invalid Date"
            />
          </div>

          <div className="col-md-12 ml-5 mt-3">
            <h4>Stepper</h4>
            <WHOStepper
              activeStep={activeStep}
              orientation="horizontal"
              alternativeLabel
              steps={[
                new StepperModel(1, "Step 1", <span>Step 1</span>),
                new StepperModel(1, "Step 2", <span>Step 2</span>),
                new StepperModel(1, "Step 3", <span>Step 3</span>),
              ]}
              onStepChange={setActiveStep}
            />
          </div>

          <div className="col-md-12 ml-5 mt-3">
            <h4>CheckBox List</h4>
            <CheckboxList
              options={[
                new MultiSelectModel(11, "Option text 1"),
                new MultiSelectModel(22, "Option text 2"),
                new MultiSelectModel(33, "Option text 3"),
                new MultiSelectModel(44, "Option text 4"),
              ]}
              selectedItems={checked}
              onClick={(newValues: Array<string | number>) => {
                setChecked(newValues);
                console.log(newValues, "newValues");
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const tabs: Array<TabModel> = [
  new TabModel(1, "Tabe 1", <>Child 1</>, false),
  new TabModel(2, "Tabe 2", <>Child 2</>, false),
  new TabModel(3, "Tabe 3", <>Child 3</>, false),
  new TabModel(4, "Tabe 4", <>Child 4</>, false),
  new TabModel(5, "Tabe 5", <>Child 5</>, false),
  new TabModel(6, "Tabe 6", <>Child 6</>, false),
  new TabModel(7, "Tabe 7", <>Child 7</>, false),
  new TabModel(8, "Tabe 8", <>Child 8</>, false),
  new TabModel(9, "Tabe 9", <>Child 9</>, false),
  new TabModel(10, "Tabe 10", <>Child 10</>, false),
];
const countries: Array<MultiSelectModel> = [
  {
    id: "15B92C19-B522-40DB-9690-6F6F2EE6C6D6",
    text: "India",
    disabled: false,
    canRemoveChip: true,
  },
  {
    id: "317B42C7-9334-4FDF-93AE-741820B32087",
    text: "Nepal",
    disabled: false,
    canRemoveChip: true,
  },
  {
    id: "9D79C3BB-CBD1-4EBC-B4BD-F2408A26262C",
    text: "Sri Lanka",
    disabled: false,
    canRemoveChip: true,
  },
  {
    id: "3EB302F3-544C-4582-A77A-186D92B54F09",
    text: "Austria",
    disabled: false,
    canRemoveChip: true,
  },
];

export default Controls;
